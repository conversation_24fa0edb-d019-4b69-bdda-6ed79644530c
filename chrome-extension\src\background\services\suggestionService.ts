import { createLogger } from '../log';
import { ContextAnalyzer, type ContextAnalysis, type TaskSuggestion } from './contextAnalyzer';
import type BrowserContext from '../browser/context';
import type { BaseChatModel } from '@langchain/core/language_models/chat_models';
import { generalSettingsStore } from '@extension/storage';
import templateStorage, { type TaskTemplate } from '@extension/storage/lib/templates/storage';

const logger = createLogger('SuggestionService');

export interface SuggestionServiceConfig {
  enabled: boolean;
  maxSuggestions: number;
  minConfidence: number;
  cacheDuration: number; // in milliseconds
}

const DEFAULT_CONFIG: SuggestionServiceConfig = {
  enabled: true,
  maxSuggestions: 5,
  minConfidence: 0.3,
  cacheDuration: 5 * 60 * 1000 // 5 minutes
};

export class SuggestionService {
  private contextAnalyzer: ContextAnalyzer;
  private browserContext: BrowserContext;
  private config: SuggestionServiceConfig;
  private lastAnalysis: ContextAnalysis | null = null;
  private lastAnalysisTime: number = 0;

  constructor(llm: BaseChatModel, browserContext: BrowserContext, config?: Partial<SuggestionServiceConfig>) {
    this.contextAnalyzer = new ContextAnalyzer(llm);
    this.browserContext = browserContext;
    this.config = { ...DEFAULT_CONFIG, ...config };
  }

  /**
   * Get contextual suggestions for the current page
   */
  async getSuggestions(forceRefresh = false): Promise<TaskSuggestion[]> {
    try {
      if (!this.config.enabled) {
        logger.debug('Suggestion service is disabled');
        return [];
      }

      // Check if we have recent analysis and don't need to refresh
      const now = Date.now();
      if (!forceRefresh && 
          this.lastAnalysis && 
          (now - this.lastAnalysisTime) < this.config.cacheDuration) {
        logger.debug('Returning cached suggestions');
        return this.filterSuggestions(this.lastAnalysis.suggestions);
      }

      // Get current page state
      const pageState = await this.browserContext.getCachedState();
      if (!pageState || !pageState.url || pageState.url === 'about:blank') {
        logger.debug('No valid page state for suggestions');
        return [];
      }

      // Analyze context
      logger.info('Generating new suggestions for page', { url: pageState.url });
      const analysis = await this.contextAnalyzer.analyzeContext(pageState);
      
      // Cache the analysis
      this.lastAnalysis = analysis;
      this.lastAnalysisTime = now;

      // Enhance suggestions with relevant templates
      const enhancedSuggestions = await this.enhanceSuggestionsWithTemplates(analysis.suggestions);

      // Filter and return suggestions
      const filteredSuggestions = this.filterSuggestions(enhancedSuggestions);

      logger.info('Generated suggestions', {
        count: filteredSuggestions.length,
        contextType: analysis.contextType,
        confidence: analysis.confidence,
        enhancedWithTemplates: enhancedSuggestions.length > analysis.suggestions.length
      });

      return filteredSuggestions;
    } catch (error) {
      logger.error('Failed to get suggestions', error);
      return [];
    }
  }

  /**
   * Get the current context analysis
   */
  getCurrentAnalysis(): ContextAnalysis | null {
    return this.lastAnalysis;
  }

  /**
   * Filter suggestions based on configuration
   */
  private filterSuggestions(suggestions: TaskSuggestion[]): TaskSuggestion[] {
    return suggestions
      .filter(suggestion => suggestion.confidence >= this.config.minConfidence)
      .slice(0, this.config.maxSuggestions);
  }

  /**
   * Update service configuration
   */
  updateConfig(newConfig: Partial<SuggestionServiceConfig>): void {
    this.config = { ...this.config, ...newConfig };
    logger.info('Suggestion service config updated', this.config);
  }

  /**
   * Clear cached analysis
   */
  clearCache(): void {
    this.lastAnalysis = null;
    this.lastAnalysisTime = 0;
    this.contextAnalyzer.clearCache();
    logger.debug('Suggestion cache cleared');
  }

  /**
   * Check if suggestions are available for current page
   */
  async hasSuggestions(): Promise<boolean> {
    const suggestions = await this.getSuggestions();
    return suggestions.length > 0;
  }

  /**
   * Get suggestion by ID
   */
  getSuggestionById(id: string): TaskSuggestion | null {
    if (!this.lastAnalysis) {
      return null;
    }
    
    return this.lastAnalysis.suggestions.find(s => s.id === id) || null;
  }

  /**
   * Track suggestion usage for analytics
   */
  async trackSuggestionUsage(suggestionId: string, action: 'viewed' | 'clicked' | 'executed'): Promise<void> {
    try {
      // Get current analytics data
      const settings = await generalSettingsStore.getSettings();
      const analytics = settings.suggestionAnalytics || {};
      
      // Initialize suggestion analytics if not exists
      if (!analytics[suggestionId]) {
        analytics[suggestionId] = {
          viewed: 0,
          clicked: 0,
          executed: 0,
          firstSeen: Date.now(),
          lastUsed: Date.now()
        };
      }

      // Update counters
      analytics[suggestionId][action]++;
      analytics[suggestionId].lastUsed = Date.now();

      // Save back to storage
      await generalSettingsStore.updateSettings({
        suggestionAnalytics: analytics
      });

      logger.debug('Tracked suggestion usage', { suggestionId, action });
    } catch (error) {
      logger.error('Failed to track suggestion usage', error);
    }
  }

  /**
   * Get usage analytics for suggestions
   */
  async getUsageAnalytics(): Promise<Record<string, any>> {
    try {
      const settings = await generalSettingsStore.getSettings();
      return settings.suggestionAnalytics || {};
    } catch (error) {
      logger.error('Failed to get usage analytics', error);
      return {};
    }
  }

  /**
   * Get relevant templates based on current context
   */
  async getRelevantTemplates(contextType?: string, category?: string): Promise<TaskTemplate[]> {
    try {
      const allTemplates = await templateStorage.getAllTemplates();

      if (!contextType && !category) {
        return allTemplates.slice(0, 3); // Return first 3 templates
      }

      // Filter templates based on context and category
      const relevantTemplates = allTemplates.filter(template => {
        const templateCategory = template.category.toLowerCase();
        const templateContent = template.content.toLowerCase();

        // Match by category
        if (category) {
          const categoryMatch = templateCategory.includes(category.toLowerCase()) ||
                               templateContent.includes(category.toLowerCase());
          if (categoryMatch) return true;
        }

        // Match by context type
        if (contextType) {
          const contextMatch = templateCategory.includes(contextType.toLowerCase()) ||
                              templateContent.includes(contextType.toLowerCase());
          if (contextMatch) return true;
        }

        return false;
      });

      return relevantTemplates.slice(0, 3); // Return top 3 matches
    } catch (error) {
      logger.error('Failed to get relevant templates', error);
      return [];
    }
  }

  /**
   * Create a template from a suggestion
   */
  async createTemplateFromSuggestion(suggestion: TaskSuggestion): Promise<TaskTemplate | null> {
    try {
      if (!suggestion.template) {
        logger.warn('Cannot create template from suggestion without template content');
        return null;
      }

      const template = await templateStorage.addTemplate(
        suggestion.title,
        suggestion.template,
        suggestion.category.replace('_', ' ')
      );

      logger.info('Created template from suggestion', {
        suggestionId: suggestion.id,
        templateId: template.id
      });

      return template;
    } catch (error) {
      logger.error('Failed to create template from suggestion', error);
      return null;
    }
  }

  /**
   * Enhance suggestions with relevant templates
   */
  async enhanceSuggestionsWithTemplates(suggestions: TaskSuggestion[]): Promise<TaskSuggestion[]> {
    try {
      const enhancedSuggestions = [...suggestions];

      // Get current context if available
      const contextType = this.lastAnalysis?.contextType;

      // Add template-based suggestions
      const relevantTemplates = await this.getRelevantTemplates(contextType);

      for (const template of relevantTemplates) {
        // Create a suggestion from the template
        const templateSuggestion: TaskSuggestion = {
          id: `template-${template.id}`,
          title: `Use Template: ${template.title}`,
          description: `Apply the "${template.title}" template to this page`,
          category: template.category.toLowerCase().replace(' ', '_') as any,
          contextType: contextType || 'unknown',
          confidence: 0.6, // Medium confidence for template suggestions
          template: template.content,
          icon: 'Save',
          priority: 2
        };

        // Add if not already present
        const exists = enhancedSuggestions.some(s =>
          s.title.toLowerCase().includes(template.title.toLowerCase())
        );

        if (!exists) {
          enhancedSuggestions.push(templateSuggestion);
        }
      }

      // Sort by priority and confidence
      return enhancedSuggestions.sort((a, b) => {
        if (a.priority !== b.priority) {
          return b.priority - a.priority;
        }
        return b.confidence - a.confidence;
      });

    } catch (error) {
      logger.error('Failed to enhance suggestions with templates', error);
      return suggestions;
    }
  }

  /**
   * Initialize the service with settings from storage
   */
  async initialize(): Promise<void> {
    try {
      const settings = await generalSettingsStore.getSettings();

      // Update config from settings
      this.updateConfig({
        enabled: settings.contextSuggestionsEnabled ?? true,
        maxSuggestions: settings.maxSuggestions ?? 5,
        minConfidence: settings.minSuggestionConfidence ?? 0.3
      });

      logger.info('Suggestion service initialized', this.config);
    } catch (error) {
      logger.error('Failed to initialize suggestion service', error);
    }
  }
}
